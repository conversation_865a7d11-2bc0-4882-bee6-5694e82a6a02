# logic_puzzle_generator.py
from __future__ import annotations
import random
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional, Callable
from ortools.sat.python import cp_model

# ---------- Core data model ----------

@dataclass(frozen=True)
class Spec:
    people: List[str]
    categories: Dict[str, List[str]]  # e.g. {"Music":[...], "Hat":[...], "Job":[...], "Hobby":[...]}

@dataclass(frozen=True)
class Goal:
    # A goal is an attribute-only test like ("Hat","Beanie") or ("Music","Jazz").
    category: str
    value: str

# Constraint “AST” (small, composable)
@dataclass(frozen=True)
class Eq:      # person has category==value
    person: str
    category: str
    value: str

@dataclass(frozen=True)
class Neq:     # person has category!=value
    person: str
    category: str
    value: str

@dataclass(frozen=True)
class LinkEq:  # For all persons: (A==a) <-> (B==b)
    category_a: str
    value_a: str
    category_b: str
    value_b: str

@dataclass(frozen=True)
class ImpEq:   # For all persons: (A==a) -> (B==b)
    category_a: str
    value_a: str
    category_b: str
    value_b: str

@dataclass(frozen=True)
class ImpNeq:  # For all persons: (A==a) -> (B!=b)
    category_a: str
    value_a: str
    category_b: str
    value_b: str

Constraint = (Eq | Neq | LinkEq | ImpEq | ImpNeq)

# ---------- Helpers: indexing and random solution ----------

def index_maps(spec: Spec) -> Tuple[Dict[str,int], Dict[str, Dict[str, int]]]:
    p2i = {p:i for i,p in enumerate(spec.people)}
    cat2vi = {cat: {v:i for i,v in enumerate(vals)} for cat,vals in spec.categories.items()}
    return p2i, cat2vi

def random_solution(spec: Spec, rng: random.Random) -> Dict[str, List[int]]:
    """Create a full consistent assignment: each category is a permutation over people."""
    n = len(spec.people)
    sol: Dict[str, List[int]] = {}
    for cat, vals in spec.categories.items():
        assert len(vals) == n, "Each category must have same cardinality as people."
        perm = list(range(n))
        rng.shuffle(perm)
        sol[cat] = perm  # person i has value index perm[i] in this category
    return sol

def pick_random_goal(spec: Spec, rng: random.Random) -> Goal:
    """Goal depends only on attributes (never on person)."""
    cat = rng.choice(list(spec.categories.keys()))
    val = rng.choice(spec.categories[cat])
    return Goal(cat, val)

# ---------- Build CP-SAT model ----------

def build_model(spec: Spec, goal: Goal, constraints: List[Constraint]):
    model = cp_model.CpModel()
    n = len(spec.people)
    p2i, cat2vi = index_maps(spec)

    # Decision variables: for each category, an array person->value_index
    cat_vars: Dict[str, List[cp_model.IntVar]] = {}
    for cat, vals in spec.categories.items():
        cat_vars[cat] = [model.NewIntVar(0, n-1, f"{cat}_{i}") for i in range(n)]
        model.AddAllDifferent(cat_vars[cat])

    def add_eq(person:str, category:str, value:str):
        model.Add(cat_vars[category][p2i[person]] == cat2vi[category][value])

    def add_neq(person:str, category:str, value:str):
        model.Add(cat_vars[category][p2i[person]] != cat2vi[category][value])

    def add_linkeq(catA:str, valA:str, catB:str, valB:str):
        a = cat2vi[catA][valA]; b = cat2vi[catB][valB]
        for i in range(n):
            ai = model.NewBoolVar(f"eq_{catA}_{a}_p{i}")
            bi = model.NewBoolVar(f"eq_{catB}_{b}_p{i}")
            model.Add(cat_vars[catA][i] == a).OnlyEnforceIf(ai)
            model.Add(cat_vars[catA][i] != a).OnlyEnforceIf(ai.Not())
            model.Add(cat_vars[catB][i] == b).OnlyEnforceIf(bi)
            model.Add(cat_vars[catB][i] != b).OnlyEnforceIf(bi.Not())
            model.Add(ai == bi)

    def add_impeq(catA:str, valA:str, catB:str, valB:str):
        a = cat2vi[catA][valA]; b = cat2vi[catB][valB]
        for i in range(n):
            cond = model.NewBoolVar(f"imp_{catA}_{a}_p{i}")
            model.Add(cat_vars[catA][i] == a).OnlyEnforceIf(cond)
            model.Add(cat_vars[catA][i] != a).OnlyEnforceIf(cond.Not())
            model.Add(cat_vars[catB][i] == b).OnlyEnforceIf(cond)

    def add_impneq(catA:str, valA:str, catB:str, valB:str):
        a = cat2vi[catA][valA]; b = cat2vi[catB][valB]
        for i in range(n):
            cond = model.NewBoolVar(f"impn_{catA}_{a}_p{i}")
            model.Add(cat_vars[catA][i] == a).OnlyEnforceIf(cond)
            model.Add(cat_vars[catA][i] != a).OnlyEnforceIf(cond.Not())
            model.Add(cat_vars[catB][i] != b).OnlyEnforceIf(cond)

    # Add constraints
    for c in constraints:
        if isinstance(c, Eq):      add_eq(c.person, c.category, c.value)
        elif isinstance(c, Neq):   add_neq(c.person, c.category, c.value)
        elif isinstance(c, LinkEq): add_linkeq(c.category_a, c.value_a, c.category_b, c.value_b)
        elif isinstance(c, ImpEq):  add_impeq(c.category_a, c.value_a, c.category_b, c.value_b)
        elif isinstance(c, ImpNeq): add_impneq(c.category_a, c.value_a, c.category_b, c.value_b)

    return model, cat_vars

# ---------- Counting & goal-uniqueness ----------

class GoalCollector(cp_model.CpSolverSolutionCallback):
    def __init__(self, spec: Spec, cat_vars: Dict[str, List[cp_model.IntVar]],
                 goal: Goal, limit: int = 2000):
        super().__init__()
        self.spec = spec
        self.cat_vars = cat_vars
        self.goal = goal
        self.limit = limit
        self.count = 0
        self.goal_people = set()  # indices of people who satisfy the goal

        _, self.cat2vi = index_maps(spec)

    def OnSolutionCallback(self):
        self.count += 1
        if self.count > self.limit:
            self.StopSearch(); return
        cat = self.goal.category
        want = self.cat2vi[cat][self.goal.value]
        for i, var in enumerate(self.cat_vars[cat]):
            if self.Value(var) == want:
                self.goal_people.add(i)
                break

def count_solutions_and_goal(model, spec, cat_vars, goal, limit=2000):
    solver = cp_model.CpSolver()
    solver.parameters.max_time_in_seconds = 5.0
    cb = GoalCollector(spec, cat_vars, goal, limit)
    solver.SearchForAllSolutions(model, cb)
    return cb.count, cb.goal_people

def is_goal_uniquely_identified(spec, goal, constraints) -> Tuple[bool, Optional[int], int]:
    """Return (unique?, person_index (if unique), num_solutions)."""
    model, cat_vars = build_model(spec, goal, constraints)
    nsol, goal_people = count_solutions_and_goal(model, spec, cat_vars, goal)
    if nsol == 0:  # contradiction
        return (False, None, 0)
    if len(goal_people) == 1:
        return (True, next(iter(goal_people)), nsol)
    return (False, None, nsol)

# ---------- Constraint sampling from a hidden solution ----------

def true_constraints_from_solution(spec: Spec, sol: Dict[str, List[int]]) -> List[Constraint]:
    """Generate a pool of *true* constraints (both positive and negative) derivable from the solution."""
    people = spec.people
    cats = list(spec.categories.keys())
    pool: List[Constraint] = []

    # Person-specific eq/neq
    for p_idx, person in enumerate(people):
        for cat in cats:
            vals = spec.categories[cat]
            v_idx = sol[cat][p_idx]
            true_val = vals[v_idx]
            pool.append(Eq(person, cat, true_val))
            # a few negatives
            for nv in vals:
                if nv != true_val:
                    pool.append(Neq(person, cat, nv))

    # Global links and implications across categories
    for i in range(len(people)):
        for c1 in cats:
            for c2 in cats:
                if c1 >= c2: 
                    continue
                v1 = spec.categories[c1][sol[c1][i]]
                v2 = spec.categories[c2][sol[c2][i]]
                # (c1==v1) <-> (c2==v2)
                pool.append(LinkEq(c1, v1, c2, v2))
                # some weaker forms
                pool.append(ImpEq(c1, v1, c2, v2))
                pool.append(ImpEq(c2, v2, c1, v1))
                # negatives against some other incorrect values
                for w in spec.categories[c2]:
                    if w != v2:
                        pool.append(ImpNeq(c1, v1, c2, w))
                for w in spec.categories[c1]:
                    if w != v1:
                        pool.append(ImpNeq(c2, v2, c1, w))
    return pool

def synthesize_puzzle(spec: Spec, rng: random.Random, max_tries=200, max_clues=30):
    """Create: (goal, selected_constraints, hidden_solution, goal_person_index, num_solutions_with_these_clues)"""
    # hidden solution & goal
    sol = random_solution(spec, rng)
    goal = pick_random_goal(spec, rng)

    # Ensure in the hidden solution there is exactly one goal-person (always true in permutations).
    # Now grow a set of clues until the goal person is unique across all completions.
    pool = true_constraints_from_solution(spec, sol)
    rng.shuffle(pool)

    selected: List[Constraint] = []
    for _ in range(max_tries):
        for _ in range(max_clues):
            if not pool:
                break
            c = pool.pop()  # try a new true constraint
            candidate = selected + [c]
            unique, who, nsol = is_goal_uniquely_identified(spec, goal, candidate)
            if nsol == 0:
                # contradiction -> reject this clue
                continue
            selected.append(c)
            if unique:
                return goal, selected, sol, who, nsol
        # If we got here, refresh the pool order and keep trying
        rng.shuffle(pool)

    raise RuntimeError("Failed to synthesize a goal-unique puzzle within limits.")

# ---------- Naming & natural language (LLM hook + safe fallback) ----------

@dataclass(frozen=True)
class TextNames:
    people: List[str]
    categories: Dict[str, List[str]]
    goal_sentence: str

def default_namer(spec: Spec, goal: Goal) -> TextNames:
    """Non-LLM fallback namer: just reuse spec strings and a simple goal sentence."""
    return TextNames(
        people=spec.people,
        categories=spec.categories,
        goal_sentence=f"Exactly one person is the target: the one whose {goal.category} is {goal.value}."
    )

def constraint_to_sentence(c: Constraint) -> str:
    def label(x): return x
    if isinstance(c, Eq):
        return f"{c.person} has {c.category} = {c.value}."
    if isinstance(c, Neq):
        return f"{c.person} does not have {c.category} = {c.value}."
    if isinstance(c, LinkEq):
        return f"The person with {c.category_a} = {c.value_a} is the same person with {c.category_b} = {c.value_b}."
    if isinstance(c, ImpEq):
        return f"If someone has {c.category_a} = {c.value_a}, then they also have {c.category_b} = {c.value_b}."
    if isinstance(c, ImpNeq):
        return f"If someone has {c.category_a} = {c.value_a}, then they do not have {c.category_b} = {c.value_b}."
    return "?"

# ---------- Public API ----------

def generate_puzzle(
    seed: int = 0,
    people: Optional[List[str]] = None,
    categories: Optional[Dict[str,List[str]]] = None,
    namer: Optional[Callable[[Spec, Goal], TextNames]] = None,
):
    rng = random.Random(seed)
    if people is None:
        people = ["Alex", "Blair", "Casey", "Devon"]
    if categories is None:
        categories = {
            "Music":   ["Jazz", "Rock", "Classical", "Hip-hop"],
            "Hat":     ["Fedora", "Beanie", "Sunhat", "Baseball cap"],
            "Job":     ["Teacher", "Chef", "Engineer", "Artist"],
            "Hobby":   ["Gardening", "Cycling", "Chess", "Photography"],
        }
    spec = Spec(people, categories)
    goal, clues, sol, goal_idx, nsol = synthesize_puzzle(spec, rng)

    namer = namer or default_namer
    names = namer(spec, goal)

    # JSON-ready mapping for validation/LLM
    json_mapping = {
        "people": names.people,
        "categories": names.categories,
        "goal": {"category": goal.category, "value": goal.value},
        "hidden_solution": sol,  # indices per category
        "goal_person_index_in_solution": goal_idx,
        "num_solutions_under_clues": nsol,
        "constraints": [c.__dict__ | {"type": type(c).__name__} for c in clues],
    }

    # Human-readable printout
    sentences = [constraint_to_sentence(c) for c in clues]

    return {
        "spec": spec,
        "goal": goal,
        "goal_idx": goal_idx,
        "json_mapping": json_mapping,
        "sentences": sentences,
    }

# ---------- Demo ----------
if __name__ == "__main__":
    puzzle = generate_puzzle(seed=42)
    print("People:", puzzle["spec"].people)
    print("Categories:", puzzle["spec"].categories)
    print("Goal:", puzzle["goal"])
    print("\nClues:")
    for s in puzzle["sentences"]:
        print("-", s)
    print("\nJSON mapping (validate/LLM input):")
    print(puzzle["json_mapping"])
